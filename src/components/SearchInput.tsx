"use client";

import { debounce } from "es-toolkit";
import { SearchIcon, XIcon } from "lucide-react";
import { useState } from "react";
import { useTranslation } from "react-i18next";
import { Button } from "./ui/button";
import { Input } from "./ui/input";

export function SearchInput({
  value: debouncedValue,
  setValue: _setValue,
}: {
  value: string;
  setValue: (searchTerm: string) => void;
}) {
  const { t } = useTranslation();
  const [value, setValue] = useState(debouncedValue);
  const debouncedSetSearchTerm = debounce(_setValue, 500);

  return (
    <div className="relative">
      <span className="absolute left-0 top-0 h-full px-3 py-2 hover:bg-transparent">
        <SearchIcon className="text-muted-foreground" />
      </span>
      <Input
        placeholder={t("Search")}
        value={value}
        onChange={(e) => {
          setValue(e.target.value);
          debouncedSetSearchTerm(e.target.value);
        }}
        className="pl-12"
      />
      {value && (
        <Button
          type="button"
          variant="ghost"
          size="sm"
          className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
          onClick={() => {
            setValue("");
            _setValue("");
          }}
        >
          <XIcon />
        </Button>
      )}
    </div>
  );
}
