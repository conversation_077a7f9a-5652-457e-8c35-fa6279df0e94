"use client";

import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { tf } from "@/lib/tf";
import Link from "next/link";
import { useRouter, useSearchParams } from "next/navigation";
import { useEffect, useMemo, useState } from "react";
import { useTranslation } from "react-i18next";
import { toast } from "sonner";
import { Data, useData } from "./data.client";
import { onDisconnectAccount, onGetConnectionLink, onSyncGetlateAccounts } from "./page.action";
import { platforms } from "./platforms";

export default function SocialAccounts() {
  const { getlateProfile } = useData();
  const searchParams = useSearchParams();
  const router = useRouter();
  const { t } = useTranslation();
  const [accounts, setAccounts] = useState(getlateProfile?.accounts || []);
  const accountsMap = useMemo(() => {
    return new Map((accounts || []).map((x) => [x.platform.toLowerCase(), x]));
  }, [accounts]);
  const connected = searchParams.get("connected");

  useEffect(() => {
    if (searchParams.get("required") === "true") {
      toast(t("Complete this step to continue"));
      router.push("/account/social-accounts");
    }
  }, [router, searchParams, t]);

  useEffect(() => {
    if (connected) {
      tf(onSyncGetlateAccounts).then((getlateProfile) => {
        if (getlateProfile?.accounts) {
          setAccounts(getlateProfile.accounts);
          router.push("/account/social-accounts");
        }
      });
    }
  }, [router, connected]);

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>{t("Social accounts")}</CardTitle>
            <CardDescription>{t("Connect your social media profiles to auto post your videos")}</CardDescription>
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="grid grid-cols-[repeat(auto-fill,minmax(12rem,1fr))] gap-4">
          {getlateProfile &&
            platforms.map((platform) => (
              <PlatformCard
                key={platform.name}
                platform={platform}
                accountsMap={accountsMap}
                getlateProfile={getlateProfile}
                setAccounts={setAccounts}
              />
            ))}
        </div>
        {accounts.length > 0 && (
          <Button asChild>
            <Link href="/">{t("Start Generating videos")}</Link>
          </Button>
        )}
      </CardContent>
    </Card>
  );
}

type Account = NonNullable<Data["getlateProfile"]>["accounts"][number];

function PlatformCard({
  platform,
  accountsMap,
  getlateProfile,
  setAccounts,
}: {
  platform: (typeof platforms)[number];
  accountsMap: Map<string, Account>;
  getlateProfile: NonNullable<Data["getlateProfile"]>;
  setAccounts: React.Dispatch<React.SetStateAction<Account[]>>;
}) {
  const { t } = useTranslation();
  const searchParams = useSearchParams();
  const [isLoading, setIsLoading] = useState(false);
  const platformName = platform.name.toLowerCase();
  const account = accountsMap.get(platform.name.toLowerCase());
  const isInProcess = isLoading || searchParams.get("connected") === platformName;

  return (
    <Card key={platform.name}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          {<platform.icon style={{ color: platform.color }} />}
          {platform.name}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <Button
          key={platform.name}
          onClick={async () => {
            setIsLoading(true);
            try {
              if (account) {
                await tf(onDisconnectAccount, account.getlateAccountId, getlateProfile?.getlateProfileId);
                setAccounts((arr) => arr.filter((x) => x.getlateAccountId !== account.getlateAccountId));
              } else {
                const res = await tf(onGetConnectionLink, platform.name);
                if (res?.authUrl) {
                  window.location.href = res?.authUrl;
                }
              }
            } catch (error) {
              console.error(error);
            }
            setIsLoading(false);
          }}
          disabled={isInProcess}
          loading={isInProcess}
          variant={account ? undefined : "outline"}
        >
          {account ? t("Disconnect") : t("Connect")}
        </Button>
      </CardContent>
    </Card>
  );
}
