"use server";

import { packageTable } from "@/database/drizzle/schema/packages";
import { errors } from "@/lib/errors";
import { getDataContext } from "@/lib/getDataContext";

interface CreatePackageData {
  name: string;
  description?: string;
  price: string;
  currency: string;
  billingInterval: "month" | "year";
  features: string[];
  isActive: boolean;
  maxVideos: number | null;
  maxStorageGB: number | null;
  priority: number;
}

export async function onCreatePackage(data: CreatePackageData) {
  const { db, session } = await getDataContext();

  if (!session?.user) return { error: errors.NotAuthenticated };
  if (session.user.role !== "admin") return { error: errors.NotAuthorized };

  try {
    const [newPackage] = await db
      .insert(packageTable)
      .values({
        ...data,
        createdAt: new Date(),
        updatedAt: new Date(),
      })
      .returning();

    if (!newPackage) {
      return { error: errors.InternalServerError };
    }

    return { success: true, package: newPackage };
  } catch (error) {
    console.error("Failed to create package:", error);
    return { error: errors.InternalServerError };
  }
}
