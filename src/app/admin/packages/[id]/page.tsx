import T from "@/components/T";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { packageTable } from "@/database/drizzle/schema/packages";
import { getDataContext } from "@/lib/getDataContext";
import { eq } from "drizzle-orm";
import { ArrowLeftIcon, CheckCircle, EditIcon, XCircle } from "lucide-react";
import Link from "next/link";
import { notFound, redirect } from "next/navigation";

interface Props {
  params: Promise<{ id: string }>;
}

export default async function PackageDetailPage({ params }: Props) {
  const { id } = await params;
  const { db, session } = await getDataContext();

  if (!session?.user) {
    redirect("/login");
  }

  if (session.user.role !== "admin") {
    redirect("/");
  }

  const [packageData] = await db.select().from(packageTable).where(eq(packageTable.id, id)).limit(1);

  if (!packageData) {
    notFound();
  }

  const formatPrice = (price: string, billingInterval: string) => {
    const amount = parseFloat(price);
    if (amount === 0) return "Free";
    return `$${amount.toFixed(2)}/${billingInterval === "year" ? "year" : "month"}`;
  };

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div>
        <h1 className="text-3xl font-bold">{packageData.name}</h1>
        <p className="text-muted-foreground">{packageData.description}</p>
      </div>
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="outline" size="sm" asChild>
            <Link href="/admin/packages">
              <ArrowLeftIcon className="h-4 w-4 mr-2" />
              <T text="Back to Packages" />
            </Link>
          </Button>
        </div>
        <Button asChild>
          <Link href={`/admin/packages/${id}/edit`}>
            <EditIcon className="h-4 w-4 mr-2" />
            <T text="Edit Package" />
          </Link>
        </Button>
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        {/* Package Details */}
        <Card>
          <CardHeader>
            <CardTitle>
              <T text="Package Details" />
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <label className="text-sm font-medium text-muted-foreground">
                <T text="Name" />
              </label>
              <p className="text-lg font-semibold">{packageData.name}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-muted-foreground">
                <T text="Description" />
              </label>
              <p>{packageData.description}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-muted-foreground">
                <T text="Price" />
              </label>
              <p className="text-lg font-semibold">{formatPrice(packageData.price, packageData.billingInterval)}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-muted-foreground">
                <T text="Billing Interval" />
              </label>
              <Badge variant="outline">
                {packageData.billingInterval === "year" ? <T text="Annual" /> : <T text="Monthly" />}
              </Badge>
            </div>
            <div>
              <label className="text-sm font-medium text-muted-foreground">
                <T text="Status" />
              </label>
              <div>
                <Badge variant={packageData.isActive ? "default" : "secondary"}>
                  {packageData.isActive ? (
                    <>
                      <CheckCircle className="h-3 w-3 mr-1" />
                      <T text="Active" />
                    </>
                  ) : (
                    <>
                      <XCircle className="h-3 w-3 mr-1" />
                      <T text="Inactive" />
                    </>
                  )}
                </Badge>
              </div>
            </div>
            <div>
              <label className="text-sm font-medium text-muted-foreground">
                <T text="Priority" />
              </label>
              <p>{packageData.priority}</p>
            </div>
          </CardContent>
        </Card>

        {/* Limits & Features */}
        <Card>
          <CardHeader>
            <CardTitle>
              <T text="Limits & Features" />
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <label className="text-sm font-medium text-muted-foreground">
                <T text="Max Videos" />
              </label>
              <p>{packageData.maxVideos ? packageData.maxVideos : <T text="Unlimited" />}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-muted-foreground">
                <T text="Max Storage (GB)" />
              </label>
              <p>{packageData.maxStorageGB ? `${packageData.maxStorageGB} GB` : <T text="Unlimited" />}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-muted-foreground">
                <T text="Features" />
              </label>
              <ul className="space-y-2 mt-2">
                {packageData.features?.map((feature, index) => (
                  <li key={index} className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-green-500 flex-shrink-0" />
                    <span className="text-sm">{feature}</span>
                  </li>
                ))}
              </ul>
            </div>
          </CardContent>
        </Card>

        {/* Stripe Integration */}
        <Card>
          <CardHeader>
            <CardTitle>
              <T text="Stripe Integration" />
            </CardTitle>
            <CardDescription>
              <T text="Stripe product and pricing information" />
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <label className="text-sm font-medium text-muted-foreground">
                <T text="Stripe Product ID" />
              </label>
              <p className="font-mono text-sm">{packageData.stripeProductId || <T text="Not set" />}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-muted-foreground">
                <T text="Stripe Price ID" />
              </label>
              <p className="font-mono text-sm">{packageData.stripePriceId || <T text="Not set" />}</p>
            </div>
          </CardContent>
        </Card>

        {/* Timestamps */}
        <Card>
          <CardHeader>
            <CardTitle>
              <T text="Timestamps" />
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <label className="text-sm font-medium text-muted-foreground">
                <T text="Created At" />
              </label>
              <p>{new Date(packageData.createdAt).toLocaleString()}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-muted-foreground">
                <T text="Updated At" />
              </label>
              <p>{new Date(packageData.updatedAt).toLocaleString()}</p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
