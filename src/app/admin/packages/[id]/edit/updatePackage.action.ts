"use server";

import { packageTable } from "@/database/drizzle/schema/packages";
import { errors } from "@/lib/errors";
import { getDataContext } from "@/lib/getDataContext";
import { eq } from "drizzle-orm";

interface UpdatePackageData {
  name: string;
  description?: string;
  price: string;
  currency: string;
  billingInterval: "month" | "year";
  features: string[];
  isActive: boolean;
  maxVideos: number | null;
  maxStorageGB: number | null;
  priority: number;
}

interface UpdatePackageParams {
  packageId: string;
  data: UpdatePackageData;
}

export async function onUpdatePackage(params: UpdatePackageParams) {
  const { db, session } = await getDataContext();

  if (!session?.user) return { error: errors.NotAuthenticated };
  if (session.user.role !== "admin") return { error: errors.NotAuthorized };

  const { packageId, data } = params;

  try {
    const [updatedPackage] = await db
      .update(packageTable)
      .set({
        ...data,
        updatedAt: new Date(),
      })
      .where(eq(packageTable.id, packageId))
      .returning();

    if (!updatedPackage) {
      return { error: errors.PackageNotFound };
    }

    return { success: true, package: updatedPackage };
  } catch (error) {
    console.error("Failed to update package:", error);
    return { error: errors.InternalServerError };
  }
}
