"use client";

import T from "@/components/T";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { PackageItem } from "@/database/drizzle/schema/packages";
import { tf } from "@/lib/tf";
import { CheckCircle, EditIcon, EyeOffIcon, PlusIcon, XCircle } from "lucide-react";
import Link from "next/link";
import { useState } from "react";
import { useTranslation } from "react-i18next";
import { toast } from "sonner";
import { useData } from "./data.client";
import { onTogglePackageStatus } from "./togglePackageStatus.action";

export function PackagesManagement() {
  const data = useData();
  const { t } = useTranslation();
  const [packages, setPackages] = useState(data.packages);
  const [loadingPackageId, setLoadingPackageId] = useState<string | null>(null);

  const handleToggleStatus = async (pkg: PackageItem) => {
    setLoadingPackageId(pkg.id);
    const result = await tf(onTogglePackageStatus, { packageId: pkg.id, isActive: !pkg.isActive });

    if (result) {
      setPackages(packages.map((p) => (p.id === pkg.id ? { ...p, isActive: !p.isActive } : p)));
      toast.success(t(pkg.isActive ? "Package deactivated successfully" : "Package activated successfully"));
    }
    setLoadingPackageId(null);
  };

  const formatPrice = (price: string, billingInterval: string) => {
    const amount = parseFloat(price);
    if (amount === 0) return t("Free");
    return `$${amount.toFixed(2)}/${billingInterval === "year" ? t("year") : t("month")}`;
  };

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">
            <T text="Packages Management" />
          </h1>
          <p className="text-muted-foreground">
            <T text="Manage subscription packages, pricing, and billing" />
          </p>
        </div>
        <Button asChild>
          <Link href="/admin/packages/new">
            <PlusIcon className="h-4 w-4 mr-2" />
            <T text="Create Package" />
          </Link>
        </Button>
      </div>

      {/* Statistics Cards */}
      <div className="grid gap-4 md:grid-cols-3">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              <T text="Total Packages" />
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{packages.length}</div>
            <p className="text-xs text-muted-foreground">
              <T text="Active:" /> {packages.filter((p) => p.isActive).length}
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              <T text="Total Subscriptions" />
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{data.subscriptionStats.total}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              <T text="Active Subscriptions" />
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{data.subscriptionStats.active}</div>
          </CardContent>
        </Card>
      </div>

      {/* Packages Table */}
      <Card>
        <CardHeader>
          <CardTitle>
            <T text="Packages" />
          </CardTitle>
          <CardDescription>
            <T text="Manage your subscription packages and pricing" />
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>
                  <T text="Name" />
                </TableHead>
                <TableHead>
                  <T text="Price" />
                </TableHead>
                <TableHead>
                  <T text="Billing" />
                </TableHead>
                <TableHead>
                  <T text="Status" />
                </TableHead>
                <TableHead>
                  <T text="Priority" />
                </TableHead>
                <TableHead>
                  <T text="Features" />
                </TableHead>
                <TableHead>
                  <T text="Actions" />
                </TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {packages.map((pkg) => (
                <TableRow key={pkg.id}>
                  <TableCell className="font-medium">
                    <div>
                      <div className="font-semibold">{pkg.name}</div>
                      <div className="text-sm text-muted-foreground">{pkg.description}</div>
                    </div>
                  </TableCell>
                  <TableCell>{formatPrice(pkg.price, pkg.billingInterval)}</TableCell>
                  <TableCell>
                    <Badge variant="outline">{pkg.billingInterval === "year" ? t("Annual") : t("Monthly")}</Badge>
                  </TableCell>
                  <TableCell>
                    <Badge variant={pkg.isActive ? "default" : "secondary"}>
                      {pkg.isActive ? (
                        <>
                          <CheckCircle className="h-3 w-3 mr-1" />
                          <T text="Active" />
                        </>
                      ) : (
                        <>
                          <XCircle className="h-3 w-3 mr-1" />
                          <T text="Inactive" />
                        </>
                      )}
                    </Badge>
                  </TableCell>
                  <TableCell>{pkg.priority}</TableCell>
                  <TableCell>
                    <div className="text-sm">
                      {pkg.features?.slice(0, 2).map((feature, index) => (
                        <div key={index} className="text-muted-foreground">
                          • {feature}
                        </div>
                      ))}
                      {pkg.features && pkg.features.length > 2 && (
                        <div className="text-muted-foreground">
                          {t("and {count} more", { count: pkg.features.length - 2 })}
                        </div>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <Button variant="outline" size="sm" asChild>
                        <Link href={`/admin/packages/${pkg.id}`}>
                          <EyeOffIcon className="h-4 w-4" />
                        </Link>
                      </Button>
                      <Button variant="outline" size="sm" asChild>
                        <Link href={`/admin/packages/${pkg.id}/edit`}>
                          <EditIcon className="h-4 w-4" />
                        </Link>
                      </Button>
                      <Button
                        variant={pkg.isActive ? "destructive" : "default"}
                        size="sm"
                        onClick={() => handleToggleStatus(pkg)}
                        disabled={loadingPackageId === pkg.id}
                        loading={loadingPackageId === pkg.id}
                      >
                        <T text={pkg.isActive ? "Deactivate" : "Activate"} />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
}
