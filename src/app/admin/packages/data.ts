"use server";

import { packageTable, subscriptionTable } from "@/database/drizzle/schema/packages";
import { getDataContext } from "@/lib/getDataContext";
import { count, desc, eq } from "drizzle-orm";

export type Data = Awaited<ReturnType<typeof getData>>;

export async function getData() {
  const { db } = await getDataContext();

  // Get all packages (including inactive ones for admin)
  const packages = await db.select().from(packageTable).orderBy(desc(packageTable.priority));

  // Get subscription statistics
  const totalSubscriptions = await db.select({ count: count() }).from(subscriptionTable);
  const activeSubscriptions = await db
    .select({ count: count() })
    .from(subscriptionTable)
    .where(eq(subscriptionTable.status, "active"));

  return {
    packages,
    subscriptionStats: {
      total: totalSubscriptions[0]?.count || 0,
      active: activeSubscriptions[0]?.count || 0,
    },
  };
}
