"use server";

import { packageTable } from "@/database/drizzle/schema/packages";
import { errors } from "@/lib/errors";
import { getDataContext } from "@/lib/getDataContext";
import { eq } from "drizzle-orm";

interface TogglePackageStatusParams {
  packageId: string;
  isActive: boolean;
}

export async function onTogglePackageStatus(params: TogglePackageStatusParams) {
  const { db, session } = await getDataContext();

  if (!session?.user) return { error: errors.NotAuthenticated };
  if (session.user.role !== "admin") return { error: errors.NotAuthorized };

  const { packageId, isActive } = params;

  try {
    const [updatedPackage] = await db
      .update(packageTable)
      .set({
        isActive,
        updatedAt: new Date(),
      })
      .where(eq(packageTable.id, packageId))
      .returning();

    if (!updatedPackage) {
      return { error: errors.PackageNotFound };
    }

    return { success: true, package: updatedPackage };
  } catch (error) {
    console.error("Failed to toggle package status:", error);
    return { error: errors.InternalServerError };
  }
}
