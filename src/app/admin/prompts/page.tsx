import T from "@/components/T";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { prompts } from "@/lib/prompts";
import { EditIcon, EyeIcon } from "lucide-react";
import Link from "next/link";

export default function PromptsPage() {
  const promptEntries = Object.entries(prompts);

  return (
    <div className="container mx-auto py-6">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-3xl font-bold">
            <T text="Prompts Management" />
          </h1>
          <p className="text-muted-foreground">
            <T text="Manage AI prompts used throughout the application" />
          </p>
        </div>
      </div>

      <div className="grid gap-4">
        {promptEntries.map(([key, prompt]) => (
          <Card key={key} className="hover:shadow-md transition-shadow">
            <CardHeader>
              <div className="flex justify-between items-start">
                <div className="flex-1">
                  <CardTitle className="text-xl">{key}</CardTitle>
                  <CardDescription className="mt-2">
                    <div className="flex items-center gap-2 mb-2">
                      <Badge variant="secondary">{prompt.model}</Badge>
                      <Badge variant="outline">
                        <T text="Max Tokens:" />
                        {prompt.maxTokens}
                      </Badge>
                    </div>
                  </CardDescription>
                </div>
                <div className="flex gap-2">
                  <Button variant="outline" size="sm" asChild>
                    <Link href={`/admin/prompts/${key}`}>
                      <EyeIcon className="h-4 w-4 mr-2" />
                      <T text="View" />
                    </Link>
                  </Button>
                  <Button size="sm" asChild>
                    <Link href={`/admin/prompts/${key}/edit`}>
                      <EditIcon className="h-4 w-4 mr-2" />
                      <T text="Edit" />
                    </Link>
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div>
                  <h4 className="font-medium text-sm text-muted-foreground mb-1">
                    <T text="Prompt Template:" />
                  </h4>
                  <p className="text-sm bg-muted p-3 rounded-md font-mono line-clamp-3">{prompt.prompt}</p>
                </div>

                {prompt.paramDescriptions && Object.keys(prompt.paramDescriptions).length > 0 && (
                  <div>
                    <h4 className="font-medium text-sm text-muted-foreground mb-2">
                      <T text="Parameters:" />
                    </h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                      {Object.entries(prompt.paramDescriptions).map(([param, description]) => (
                        <div key={param} className="text-sm">
                          <code className="bg-muted px-1 py-0.5 rounded text-xs font-mono">{param}</code>
                          <span className="text-muted-foreground ml-2">{description}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
}
