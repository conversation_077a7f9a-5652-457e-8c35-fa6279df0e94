## Overview

We follow a consistent structure for form handling, server actions, and client interactions.
This document outlines **best practices**, **code snippets**, and **usage conventions** to keep the codebase clean and scalable.

---

## 📂 Project Conventions

### Forms

- Use `react-hook-form` + `zod` for validation.
- Always import input elements from `@/components/Field`. Create a custom `Field` if necessary.
- Use `tf` wrapper for server actions to handle errors gracefully.
- Show success/failure messages with `sonner`’s `toast`.
- Ensure all labels, placeholders, and messages are internationalized via `t("...")`.

### Example Form Snippet

```tsx
import { useState } from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { useTranslation } from "react-i18next";
import { toast } from "sonner";
import { Field } from "@/components/Field";
import { Button } from "@/components/ui/button";
import { Form } from "@/components/ui/form";
import { tf } from "@/lib/tf";
import z from "zod";
import { onSave } from "./ExampleForm.action";

const formSchema = z.object({
  name: z.string().nonempty(),
  email: z.string().email(),
});

export function ExampleForm({ userData }: { userData: { name: string; email: string } }) {
  const { t } = useTranslation();
  const [isLoading, setIsLoading] = useState(false);

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: userData.name,
      email: userData.email,
    },
  });

  const onSubmit = async (values: z.infer<typeof formSchema>) => {
    setIsLoading(true);
    const res = await tf(onSave, values);
    if (res) {
      toast.success(t("Form action successful!"));
    }
    setIsLoading(false);
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <Field name="name" label={t("Full Name")} control={form.control} />
        <Field name="email" label={t("Email Address")} control={form.control} input={{ props: { type: "email" } }} />
        <Button type="submit" disabled={isLoading} loading={isLoading}>
          {t("Submit")}
        </Button>
      </form>
    </Form>
  );
}
```

---

## 🧩 Server Actions

- Keep all server actions colocated with the feature in a `.action.ts` file.
- Always return a structured response (success/error).
- Wrap client calls with `tf` for safe handling.

```ts
// ExampleForm.action.ts
"use server";

import { errors } from "@/lib/errors";
import { getDataContext } from "@/lib/getDataContext";

export async function onSave(values: { name: string; email: string }) {
  const context = await getDataContext();

  if (!context.session?.user) return { error: errors.NotAuthenticated };

  return await connectToAccount(context.db, context.session?.user, socialPlatform);
}
```

---

## 🚨 Error Handling

The user-facing errors need to be **minimal and reusable**.
Define them in a central enum at `src/lib/errors.ts`:

```ts
// src/lib/errors.ts
export enum errors {
  NotAuthenticated = "Not authenticated",
  NotAuthorized = "Not authorized",
  NoConnectedSocialAccount = "No social accounts connected",
  InternalServerError = "Internal server error",
  // ...
}
```

### Rules

- **Only use these errors in server actions** (e.g., `.action.ts` files).
- Internal backend services do **not** need to use this enum; they can `console.error` or log detailed messages as needed.
- On the client side, errors are already handled by `tf`, so you should **not** import or reference the `errors` enum in React components.

This ensures:

- End-user messages remain minimal and consistent.
- Developers keep detailed internal errors out of the UI.
- All user-facing error text is managed in one place (`src/lib/errors.ts`).

---

## 🗣️ Internationalization (i18n)

- Use `react-i18next` for all UI strings.
- No hardcoded text allowed in forms, buttons, or error messages.

✅ Good

```tsx
<Field name="name" label={t("Full Name")} control={form.control} />
```

❌ Bad

```tsx
<Field name="name" label="Full Name" control={form.control} />
```

### RSC

For react server components use `T` component.

```tsx
<T text="Full Name" />
```

For react client components use `t` hook.

```tsx
const { t } = useTranslation();
```

---

## ✅ Checklist Before Commit

- [ ] All forms use `Field` from `@/components/Field`.
- [ ] All validation schemas use `zod`. Use nonempty() for required fields.
- [ ] All text is internationalized (`t("...")`).
- [ ] Server actions live in `.action.ts`.
- [ ] API calls wrapped in `tf`.
- [ ] UI shows feedback (`toast.success` / `toast.error`).
- [ ] **Server function errors are defined in `src/lib/errors.ts` and never hardcoded.**

---
